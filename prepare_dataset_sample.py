import pandas as pd
import os
import shutil
import json
from pathlib import Path

# Load your CSV file
csv_path = '/mnt/data/ajohn/stylegan3/datasets/eye2gene_new_filepaths/all_baf_valid_50deg_filtered_train_0.csv'
df = pd.read_csv(csv_path)

# Take a small sample - adjust the number as needed
# This will take 100 random images from each class
sample_size_per_class = 100
sampled_df = df.groupby('gene').apply(lambda x: x.sample(min(len(x), sample_size_per_class))).reset_index(drop=True)
print(f"Sampled {len(sampled_df)} images from {len(df)} total images")

# Create a temporary directory to store the images
temp_dir = '/mnt/data/ajohn/stylegan3/datasets/temp_images_sample'
os.makedirs(temp_dir, exist_ok=True)

# Get the column names from your CSV
file_col = 'file.path'  # Adjust if your column name is different
label_col = 'gene'      # Adjust if your column name is different

# Load class mapping if available
class_mapping_path = '/mnt/data/ajohn/stylegan3/classes_mapping.json'
if os.path.exists(class_mapping_path):
    with open(class_mapping_path, 'r') as f:
        class_mapping = json.load(f)
else:
    # Create class mapping from unique genes
    unique_genes = sampled_df[label_col].unique()
    class_mapping = {gene: idx for idx, gene in enumerate(unique_genes)}
    # Save the mapping for future reference
    with open('generated_class_mapping.json', 'w') as f:
        json.dump(class_mapping, f)

# Prepare dataset.json structure
dataset_labels = []

# Copy images to the temporary directory
for idx, row in sampled_df.iterrows():
    src_path = row[file_col]
    gene = row[label_col]
    
    # Create destination path with a structured naming convention
    idx_str = f'{idx:08d}'
    dst_dir = os.path.join(temp_dir, idx_str[:5])
    os.makedirs(dst_dir, exist_ok=True)
    
    dst_filename = f'img{idx_str}.png'
    dst_path = os.path.join(dst_dir, dst_filename)
    
    # Copy the file
    try:
        shutil.copy2(src_path, dst_path)
        print(f"Copied {src_path} to {dst_path}")
        
        # Add to dataset labels
        rel_path = os.path.join(idx_str[:5], dst_filename).replace('\\', '/')
        class_idx = class_mapping[gene]
        dataset_labels.append([rel_path, class_idx])
    except Exception as e:
        print(f"Error copying {src_path}: {e}")

# Create dataset.json file
dataset_json = {
    "labels": dataset_labels
}

with open(os.path.join(temp_dir, 'dataset.json'), 'w') as f:
    json.dump(dataset_json, f)

print(f"Images organized in {temp_dir}")
print(f"Created dataset.json with {len(dataset_labels)} labeled images")
print(f"Class mapping: {class_mapping}")