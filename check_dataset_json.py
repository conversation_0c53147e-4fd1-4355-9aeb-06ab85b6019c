import json
import os
import zipfile
import tempfile

# Path to your dataset zip
dataset_path = '/mnt/data/ajohn/stylegan3/datasets/eye2gene_sample_256x256.zip'

# Create a temporary directory
with tempfile.TemporaryDirectory() as temp_dir:
    # Extract dataset.json from the zip file
    with zipfile.ZipFile(dataset_path, 'r') as zip_ref:
        try:
            zip_ref.extract('dataset.json', temp_dir)
            # Load and print the dataset.json content
            with open(os.path.join(temp_dir, 'dataset.json'), 'r') as f:
                dataset_json = json.load(f)
                print("dataset.json structure:")
                print(json.dumps(dataset_json, indent=2)[:500] + "...")  # Print first 500 chars
                
                # Check if labels exist and are properly formatted
                if 'labels' in dataset_json:
                    labels = dataset_json['labels']
                    print(f"\nFound {len(labels)} labels")
                    print("First 5 labels:", labels[:5])
                    
                    # Check label format
                    if len(labels) > 0:
                        if isinstance(labels[0], list) and len(labels[0]) == 2:
                            print("Label format looks correct: [filename, class_idx]")
                        else:
                            print("WARNING: Unexpected label format:", labels[0])
                else:
                    print("\nWARNING: No 'labels' key found in dataset.json")
        except KeyError:
            print("ERROR: dataset.json not found in the zip file")