import pandas as pd
import os
import shutil
import json
import zipfile
import tempfile

# Load your CSV file
csv_path = '/mnt/data/ajohn/stylegan3/datasets/eye2gene_new_filepaths/all_baf_valid_50deg_filtered_train_0.csv'
df = pd.read_csv(csv_path)

# Take a small sample - adjust the number as needed
sample_size_per_class = 50
sampled_df = df.groupby('gene').apply(lambda x: x.sample(min(len(x), sample_size_per_class))).reset_index(drop=True)
print(f"Sampled {len(sampled_df)} images from {len(df)} total images")

# Create a temporary directory to store the images
temp_dir = '/mnt/data/ajohn/stylegan3/datasets/temp_images_proper'
os.makedirs(temp_dir, exist_ok=True)

# Get the column names from your CSV
file_col = 'file.path'
label_col = 'gene'

# Create class mapping from unique genes
unique_genes = sampled_df[label_col].unique()
class_mapping = {gene: idx for idx, gene in enumerate(unique_genes)}
print(f"Class mapping: {class_mapping}")

# Save the mapping for future reference
with open(os.path.join(temp_dir, 'class_mapping.json'), 'w') as f:
    json.dump(class_mapping, f)

# Prepare dataset.json structure - IMPORTANT: This must be a list of [filename, class_idx] pairs
dataset_labels = []

# Copy images to the temporary directory with a flat structure
for idx, row in sampled_df.iterrows():
    src_path = row[file_col]
    gene = row[label_col]
    
    # Simple naming convention for clarity
    dst_filename = f"{idx:06d}_{class_mapping[gene]}.png"
    dst_path = os.path.join(temp_dir, dst_filename)
    
    # Copy the file
    try:
        shutil.copy2(src_path, dst_path)
        print(f"Copied {src_path} to {dst_path}")
        
        # Add to dataset labels - IMPORTANT: This must be [filename, class_idx]
        class_idx = class_mapping[gene]
        dataset_labels.append([dst_filename, class_idx])
    except Exception as e:
        print(f"Error copying {src_path}: {e}")

# Create dataset.json file with the EXACT format StyleGAN3 expects
dataset_json = {
    "labels": dataset_labels
}

with open(os.path.join(temp_dir, 'dataset.json'), 'w') as f:
    json.dump(dataset_json, f)

print(f"Images organized in {temp_dir}")
print(f"Created dataset.json with {len(dataset_labels)} labeled images")

# Verify the dataset.json structure
with open(os.path.join(temp_dir, 'dataset.json'), 'r') as f:
    verify = json.load(f)
    print("\nVerifying dataset.json structure:")
    print(f"Contains 'labels' key: {'labels' in verify}")
    if 'labels' in verify:
        print(f"Number of labels: {len(verify['labels'])}")
        print(f"First few labels: {verify['labels'][:3]}")
        print(f"Label format: {type(verify['labels'][0])}")
        print(f"Label example: {verify['labels'][0]}")