# GAN I/O SETTINGS
model_name: stylegan3-t  # change to stylegan3-t or stylegan3-r
z_dim: 512  # keep the same
output_im_resolution: 512  # keep the same
generate_randomly: true  # keep the same
weights_dir: ./training-runs/your-stylegan3-run-directory  # update to StyleGAN3 checkpoint directory
weights_path: network-snapshot-XXXXX.pkl  # update to your StyleGAN3 checkpoint file
classes: classes.txt  # keep the same
class_mapping: classes_mapping.json  # keep the same
n_samples: -1  # keep the same
real_data_path: datasets/eye2gene_new_filepaths/all_baf_valid_50deg_filtered_train_0.csv  # keep the same
save_as: StyleGAN3_512_rebalanced_datasetAJ  # update to reflect StyleGAN3 usage

# StyleGAN3-specific settings
truncation_psi: 0.7  # you can adjust this (lower values = less variation but higher quality)
noise_mode: "const"  # keep the same

# Additional StyleGAN3 settings
cfg: "stylegan3-t"  # or "stylegan3-r" for the radial variant
