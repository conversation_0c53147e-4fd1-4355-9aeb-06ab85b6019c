# DATA SETTINGS
train_data_file: datasets/eye2gene_new_filepaths/all_baf_valid_50deg_filtered_train_0.csv
test_data_file: datasets/eye2gene_new_filepaths/all_baf_valid_50deg_filtered_val_0_edited.csv
filenames_col: file.path
labels_col: gene
train_classes: classes.txt
class_mapping: classes_mapping.json
transformations:
  crop:
  resize_dim: 256  # Consider 512 for StyleGAN3 if GPU memory allows
  random_flip: 1
  grayscale: 1
  normalize: 1

# GAN I/O SETTINGS
model: stylegan3-t  # Change to stylegan3-t or stylegan3-r
z_dim: 512
output_im_resolution: 256  # Consider 512 for StyleGAN3 if GPU memory allows

# TRAINING SETTINGS
epochs: 1000
loss_fn: nsgan  # StyleGAN3 uses the same loss as StyleGAN2
batch_size: 16  # Reduced from 21 as StyleGAN3 uses more memory
n_disc_updates: 1
gen_lr: 0.0025  # StyleGAN3 default is 0.0025 (vs 0.002 for StyleGAN2)
disc_lr: 0.0025
beta1: 0.0
beta2: 0.99
display_step: 300
calc_fid: 1  # Enable FID calculation for StyleGAN3
n_samples_to_generate: 9
save_checkpoint_steps: 50
save_tensorboard: 1
parallel: 1
device_ids: [0, 1]
save_weights_as: "stylegan3-t_256_aj"  # Updated name to reflect StyleGAN3

# StyleGAN3-specific settings
cfg: "stylegan3-t"  # or "stylegan3-r" for rotation equivariance
gamma: 8.2  # R1 regularization weight (8.2 for stylegan3-t at 512px, 16.4 for stylegan3-r)
mirror: 1  # Enable dataset mirroring/augmentation
kimg: 25000  # Total training duration in thousands of images
metrics: "fid50k_full"  # Metrics to compute during training

# Optional transfer learning from pre-trained model
# resume: "https://api.ngc.nvidia.com/v2/models/nvidia/research/stylegan3/versions/1/files/stylegan3-t-ffhq-1024x1024.pkl"
