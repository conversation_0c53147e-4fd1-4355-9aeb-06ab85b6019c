# DATA
real_data_file: /mnt/data/ajohn/stylegan3/datasets/syntheye/faf_dataset_cleaned.csv
synthetic_data_file: /mnt/data/ajohn/stylegan3/synthetic_datasets/stylegan3_exp_AJ/generated_examples.csv
filenames_col: file.path
labels_col: gene
classes: all
transformations:
  resize_dim: 512
  grayscale: 1

# EVALUATION SETTINGS
save_dir: results/stylegan3_run_results
similarity_check:
  compute: 0
  process_images:
    alpha: 1.0
    beta: 0.0
    filtering:
      kernel:
      size:
    thresholding:
      function:
      size:
  similarity_metric: MSE
  save_most_similar: 1
  save_most_different: 1
quality_check:
  compute: 1
  quality_metric: BRISQUE

# StyleGAN3-specific metrics
equivariance_metrics:
  compute: 1
  eqt50k_int: 1  # Translation equivariance
  eqr50k: 1      # Rotation equivariance (especially important if using stylegan3-r)

fid_metrics:
  compute: 1
  fid50k_full: 1

# Uncomment these if you need them
#  fid_imagenet: 0
#  fid_eye2gene: 0
#  class_preds_eye2gene: 0
#  save_as:
