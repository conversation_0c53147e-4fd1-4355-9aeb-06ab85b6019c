---
name: Bug report
about: Create a report to help us improve
title: ''
labels: ''
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. In '...' directory, run command '...'
2. See error (copy&paste full log, including exceptions and **stacktraces**).

Please copy&paste text instead of screenshots for better searchability.

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Desktop (please complete the following information):**
 - OS: [e.g. Linux Ubuntu 20.04, Windows 10]
 - PyTorch version (e.g., pytorch 1.9.0)
 - CUDA toolkit version (e.g., CUDA 11.4)
 - NVIDIA driver version
 - GPU [e.g., Titan V, RTX 3090]
 - Docker: did you use Docker?  If yes, specify docker image URL (e.g., nvcr.io/nvidia/pytorch:21.08-py3)

**Additional context**
Add any other context about the problem here.
